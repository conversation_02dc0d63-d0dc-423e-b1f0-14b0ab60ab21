import { MCPClient } from "@mastra/mcp";
import { Agent } from "@mastra/core";
import OpenAI from "openai";

// MCP Client ayarları - kendi kull<PERSON> adın, proje adın ve api key ile değ<PERSON>tir
const mcp = new MCPClient({
  servers: {
    smithery: {
      type: "http",
      url: "https://server.smithery.ai/@Busra-ozer/simple-mcp/mcp?api_key=17a787e5-443b-4a4a-82b6-0a97bc0a25dc",
    },
  },
});

// OpenAI client, API key'i .env'den alıyor
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const agent = new Agent({
  client: openai,
  mcpClient: mcp,
  name: "SimpleAgent",
  model: "gpt-4o-mini",
});

async function test() {
  const response = await agent.invoke({
    prompt: "merhaba dünya yazısını ters çevir",
  });
  console.log(response);
}

test();
